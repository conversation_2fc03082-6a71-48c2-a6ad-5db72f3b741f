"""
MT5交易策略生成器
功能：连接MT5，获取历史数据，生成简单的交易信号，导出CSV文件供MQL5使用
"""

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
from config import STRATEGY_CONFIG, OUTPUT_CONFIG, get_mt5_timeframe, validate_config

class SimpleStrategy:
    def __init__(self, symbol="EURUSD", timeframe=mt5.TIMEFRAME_H1, lot_size=0.01, 
                 stop_loss_points=100, take_profit_points=200):
        """
        初始化策略参数
        
        Args:
            symbol: 交易品种
            timeframe: 时间周期
            lot_size: 交易手数
            stop_loss_points: 止损点数
            take_profit_points: 止盈点数
        """
        self.symbol = symbol
        self.timeframe = timeframe
        self.lot_size = lot_size
        self.stop_loss_points = stop_loss_points
        self.take_profit_points = take_profit_points
        self.signals = []
        
    def connect_mt5(self):
        """连接MT5"""
        if not mt5.initialize():
            print(f"初始化MT5失败，错误代码：{mt5.last_error()}")
            return False
        
        print(f"MT5版本：{mt5.version()}")
        print(f"连接成功")
        return True
    
    def get_historical_data(self, days=30, start_date=None, end_date=None, use_date_range=False):
        """
        获取历史数据
        
        Args:
            days: 获取多少天的历史数据（当use_date_range为False时使用）
            start_date: 开始日期字符串，格式为'YYYY-MM-DD'（当use_date_range为True时使用）
            end_date: 结束日期字符串，格式为'YYYY-MM-DD'（当use_date_range为True时使用）
            use_date_range: 是否使用日期范围而不是相对天数
            
        Returns:
            DataFrame: 包含OHLCV数据的DataFrame
        """
        if use_date_range and start_date and end_date:
            # 使用固定日期范围
            try:
                utc_from = datetime.strptime(start_date, '%Y-%m-%d')
                utc_to = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)  # 加1天以包含结束日期
                
                print(f"获取数据时间范围: {utc_from.strftime('%Y-%m-%d')} 至 {utc_to.strftime('%Y-%m-%d')}")
                
                # 计算天数差以估算需要获取的数据点数量
                days_diff = (utc_to - utc_from).days
                
                # 获取数据
                rates = mt5.copy_rates_range(self.symbol, self.timeframe, utc_from, utc_to)
            except ValueError as e:
                print(f"日期格式错误: {e}")
                return None
        else:
            # 使用相对天数（原有逻辑）
            utc_from = datetime.now() - timedelta(days=days)
            rates = mt5.copy_rates_from(self.symbol, self.timeframe, utc_from, days * 24)
        
        if rates is None:
            print(f"获取{self.symbol}数据失败")
            return None
        
        # 转换为DataFrame
        df = pd.DataFrame(rates)
        df['time'] = pd.to_datetime(df['time'], unit='s')
        
        print(f"获取到{len(df)}条{self.symbol}数据")
        print(f"数据时间范围: {df['time'].min()} 至 {df['time'].max()}")
        return df
    
    def calculate_indicators(self, df):
        """
        计算技术指标
        这里使用简单的移动平均线策略作为示例
        
        Args:
            df: 价格数据DataFrame
            
        Returns:
            DataFrame: 添加了指标的DataFrame
        """
        # 计算移动平均线
        df['ma_fast'] = df['close'].rolling(window=10).mean()  # 快线
        df['ma_slow'] = df['close'].rolling(window=20).mean()  # 慢线
        
        # 计算RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        return df
    
    def generate_signals(self, df):
        """
        生成交易信号
        策略逻辑：
        - 买入信号：快线上穿慢线且RSI < 70
        - 卖出信号：快线下穿慢线且RSI > 30
        
        Args:
            df: 包含指标的DataFrame
        """
        signals = []
        
        for i in range(1, len(df)):
            current_time = df.iloc[i]['time']
            current_price = df.iloc[i]['close']
            
            # 获取当前和前一个周期的指标值
            ma_fast_current = df.iloc[i]['ma_fast']
            ma_fast_prev = df.iloc[i-1]['ma_fast']
            ma_slow_current = df.iloc[i]['ma_slow']
            ma_slow_prev = df.iloc[i-1]['ma_slow']
            rsi_current = df.iloc[i]['rsi']
            
            # 跳过NaN值
            if pd.isna(ma_fast_current) or pd.isna(ma_slow_current) or pd.isna(rsi_current):
                continue
            
            # 买入信号：快线上穿慢线且RSI < 70
            if (ma_fast_prev <= ma_slow_prev and ma_fast_current > ma_slow_current and 
                rsi_current < 70):
                
                signal = {
                    'time': current_time,
                    'type': 'BUY',
                    'price': current_price,
                    'lot_size': self.lot_size,
                    'stop_loss': current_price - (self.stop_loss_points * self.get_point_value()),
                    'take_profit': current_price + (self.take_profit_points * self.get_point_value())
                }
                signals.append(signal)
            
            # 卖出信号：快线下穿慢线且RSI > 30
            elif (ma_fast_prev >= ma_slow_prev and ma_fast_current < ma_slow_current and 
                  rsi_current > 30):
                
                signal = {
                    'time': current_time,
                    'type': 'SELL',
                    'price': current_price,
                    'lot_size': self.lot_size,
                    'stop_loss': current_price + (self.stop_loss_points * self.get_point_value()),
                    'take_profit': current_price - (self.take_profit_points * self.get_point_value())
                }
                signals.append(signal)
        
        self.signals = signals
        print(f"生成了{len(signals)}个交易信号")
        return signals
    
    def get_point_value(self):
        """获取点值"""
        symbol_info = mt5.symbol_info(self.symbol)
        if symbol_info is None:
            return 0.0001  # 默认值
        return symbol_info.point
    
    def export_to_csv(self, filename="trading_signals.csv"):
        """
        导出交易信号到CSV文件
        
        Args:
            filename: CSV文件名
        """
        if not self.signals:
            print("没有交易信号可导出")
            return
        
        df_signals = pd.DataFrame(self.signals)
        
        # 格式化时间为MQL5可读格式
        df_signals['time_str'] = df_signals['time'].dt.strftime('%Y.%m.%d %H:%M:%S')
        
        # 重新排列列顺序
        df_export = df_signals[['time_str', 'type', 'price', 'lot_size', 'stop_loss', 'take_profit']]
        df_export.columns = ['DateTime', 'OrderType', 'Price', 'LotSize', 'StopLoss', 'TakeProfit']
        
        # 导出到多个位置，确保测试器能找到文件
        paths_to_export = [
            filename,  # 当前目录
            os.path.join(os.getenv('APPDATA'), 'MetaQuotes', 'Terminal', 'Common', 'Files', filename),  # 共享文件夹
        ]
        
        # 尝试导出到测试器文件夹
        tester_dir = os.path.join(os.getenv('APPDATA'), 'MetaQuotes', 'Tester')
        if os.path.exists(tester_dir):
            for folder in os.listdir(tester_dir):
                if os.path.isdir(os.path.join(tester_dir, folder)):
                    agent_dir = os.path.join(tester_dir, folder, 'Agent-127.0.0.1-3000', 'MQL5', 'Files')
                    if os.path.exists(agent_dir):
                        paths_to_export.append(os.path.join(agent_dir, filename))
        
        # 导出到所有路径
        for path in paths_to_export:
            try:
                # 确保目录存在
                os.makedirs(os.path.dirname(path), exist_ok=True)
                df_export.to_csv(path, index=False)
                print(f"交易信号已导出到：{path}")
            except Exception as e:
                print(f"导出到 {path} 失败: {e}")
        
        # 显示前几行数据
        print("\n前5个交易信号：")
        print(df_export.head())
    
    def run_strategy(self, days=30, output_file="trading_signals.csv", start_date=None, end_date=None, use_date_range=False):
        """
        运行完整策略
        
        Args:
            days: 分析多少天的历史数据（当use_date_range为False时使用）
            output_file: 输出CSV文件名
            start_date: 开始日期字符串（当use_date_range为True时使用）
            end_date: 结束日期字符串（当use_date_range为True时使用）
            use_date_range: 是否使用日期范围而不是相对天数
        """
        print("开始运行交易策略...")
        
        # 连接MT5
        if not self.connect_mt5():
            return
        
        try:
            # 获取历史数据
            df = self.get_historical_data(days, start_date, end_date, use_date_range)
            if df is None:
                return
            
            # 计算技术指标
            df = self.calculate_indicators(df)
            
            # 生成交易信号
            self.generate_signals(df)
            
            # 导出CSV
            self.export_to_csv(output_file)
            
            print("策略运行完成！")
            
        finally:
            # 断开MT5连接
            mt5.shutdown()

def main():
    """主函数"""
    # 验证配置
    if not validate_config():
        print("配置验证失败，程序退出")
        return

    # 从配置文件读取参数
    config = STRATEGY_CONFIG
    output_config = OUTPUT_CONFIG

    # 创建策略实例
    strategy = SimpleStrategy(
        symbol=config['symbol'],
        timeframe=get_mt5_timeframe(config['timeframe']),
        lot_size=config['lot_size'],
        stop_loss_points=config['stop_loss_points'],
        take_profit_points=config['take_profit_points']
    )

    # 运行策略
    strategy.run_strategy(
        days=config['history_days'],
        output_file=output_config['csv_filename'],
        start_date=config.get('start_date'),
        end_date=config.get('end_date'),
        use_date_range=config.get('use_date_range', False)
    )

if __name__ == "__main__":
    main()




