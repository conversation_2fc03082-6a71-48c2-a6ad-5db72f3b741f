"""
快速运行脚本 - 一键生成交易信号
"""

import sys
import os
from datetime import datetime

def main():
    """主函数"""
    print("=" * 60)
    print("MT5交易策略信号生成器")
    print("=" * 60)
    print(f"运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # 导入策略生成器
        from strategy_generator import main as run_strategy
        
        # 运行策略
        print("开始生成交易信号...")
        run_strategy()
        
        print()
        print("=" * 60)
        print("信号生成完成！")
        print("=" * 60)
        print()
        print("下一步操作：")
        print("1. 检查生成的 trading_signals.csv 文件")
        print("2. 将 CSVSignalEA.mq5 复制到 MT5 的 Experts 文件夹")
        print("3. 在 MT5 中编译 EA")
        print("4. 在策略测试器中运行回测")
        print()
        print("注意：确保 trading_signals.csv 文件在 MT5 可以访问的位置")
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装所需的Python包：")
        print("pip install -r requirements.txt")
        
    except Exception as e:
        print(f"运行错误: {e}")
        print("请检查配置文件和MT5连接")
    
    finally:
        input("\n按回车键退出...")

if __name__ == "__main__":
    main()
