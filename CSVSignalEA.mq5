//+------------------------------------------------------------------+
//|                                                  CSVSignalEA.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"

//--- 输入参数
input string CSVFileName = "trading_signals.csv";  // CSV文件名
input bool   EnableTrading = true;                 // 是否启用交易
input int    MagicNumber = 123456;                 // 魔术数字
input double RiskPercent = 1.0;                    // 风险百分比
input bool   UseFixedLot = true;                   // 使用固定手数
input double FixedLotSize = 0.01;                  // 固定手数大小

//--- 全局变量
struct TradeSignal
{
   datetime time;
   string   order_type;
   double   price;
   double   lot_size;
   double   stop_loss;
   double   take_profit;
};

TradeSignal signals[];
int signal_count = 0;
int current_signal_index = 0;
bool csv_loaded = false;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("CSVSignalEA 初始化开始...");
   
   // 打印当前工作目录和文件路径
   Print("当前工作目录: ", TerminalInfoString(TERMINAL_DATA_PATH));
   Print("当前测试目录: ", TerminalInfoString(TERMINAL_PATH));
   Print("尝试加载文件: ", CSVFileName);
   
   // 打印更多文件系统信息
   Print("文件是否存在(直接路径): ", FileIsExist(CSVFileName));
   Print("文件是否存在(MQL5\\Files): ", FileIsExist("MQL5\\Files\\" + CSVFileName));
   Print("文件是否存在(..\\MQL5\\Files): ", FileIsExist("..\\MQL5\\Files\\" + CSVFileName));
   
   // 加载CSV文件
   if(LoadCSVSignals())
   {
      csv_loaded = true;
      Print("成功加载 ", signal_count, " 个交易信号");
   }
   else
   {
      Print("加载CSV文件失败");
      return INIT_FAILED;
   }
   
   Print("CSVSignalEA 初始化完成");
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print("CSVSignalEA 停止运行，原因代码: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   if(!csv_loaded || !EnableTrading)
      return;
   
   // 检查是否有新的交易信号需要执行
   CheckAndExecuteSignals();
}

//+------------------------------------------------------------------+
//| 加载CSV交易信号                                                  |
//+------------------------------------------------------------------+
bool LoadCSVSignals()
{
   // 尝试多种可能的文件路径
   string file_paths[] = {
      CSVFileName,                                // 直接使用输入的文件名
      "MQL5\\Files\\" + CSVFileName,              // MQL5\Files目录
      "..\\MQL5\\Files\\" + CSVFileName,          // 相对于Experts目录的Files目录
      "..\\Files\\" + CSVFileName,                // 相对于Experts目录的Files目录
      "tester\\files\\" + CSVFileName,            // 测试器专用目录
      "\\MQL5\\Files\\" + CSVFileName             // 从根目录开始的绝对路径
   };
   
   int file_handle = INVALID_HANDLE;
   string used_path = "";
   
   // 尝试所有可能的路径
   for(int i=0; i<ArraySize(file_paths); i++)
   {
      Print("尝试打开文件: ", file_paths[i]);
      file_handle = FileOpen(file_paths[i], FILE_READ|FILE_CSV|FILE_ANSI);
      if(file_handle != INVALID_HANDLE)
      {
         used_path = file_paths[i];
         Print("成功打开文件: ", used_path);
         break;
      }
   }
   
   if(file_handle == INVALID_HANDLE)
   {
      Print("无法打开文件，错误代码: ", GetLastError());
      Print("请确保文件位于正确位置，并检查文件名是否正确");
      return false;
   }
   
   // 跳过标题行
   string header = FileReadString(file_handle);
   Print("CSV标题行: ", header);
   
   // 动态调整数组大小
   ArrayResize(signals, 1000); // 预分配空间
   signal_count = 0;
   
   // 读取数据行
   while(!FileIsEnding(file_handle) && signal_count < 1000)
   {
      string line = FileReadString(file_handle);
      if(line == "")
         continue;
         
      // 解析CSV行
      string parts[];
      int part_count = StringSplit(line, ',', parts);
      
      if(part_count >= 6)
      {
         // 解析时间 (格式: YYYY.MM.DD HH:MM:SS)
         datetime signal_time = StringToTime(parts[0]);
         
         signals[signal_count].time = signal_time;
         signals[signal_count].order_type = parts[1];
         signals[signal_count].price = StringToDouble(parts[2]);
         signals[signal_count].lot_size = UseFixedLot ? FixedLotSize : StringToDouble(parts[3]);
         signals[signal_count].stop_loss = StringToDouble(parts[4]);
         signals[signal_count].take_profit = StringToDouble(parts[5]);
         
         signal_count++;
      }
   }
   
   FileClose(file_handle);
   
   // 调整数组到实际大小
   ArrayResize(signals, signal_count);
   
   return signal_count > 0;
}

//+------------------------------------------------------------------+
//| 检查并执行交易信号                                               |
//+------------------------------------------------------------------+
void CheckAndExecuteSignals()
{
   datetime current_time = TimeCurrent();
   
   // 检查当前时间是否匹配任何信号时间
   for(int i = current_signal_index; i < signal_count; i++)
   {
      // 如果信号时间已经过去，执行交易
      if(signals[i].time <= current_time)
      {
         ExecuteSignal(signals[i]);
         current_signal_index = i + 1;
      }
      else
      {
         break; // 信号按时间排序，后面的信号时间还没到
      }
   }
}

//+------------------------------------------------------------------+
//| 执行交易信号                                                     |
//+------------------------------------------------------------------+
void ExecuteSignal(TradeSignal &signal)
{
   if(!EnableTrading)
      return;
   
   // 检查是否已经有相同魔术数字的订单
   if(CountOrdersByMagic() >= 1) // 限制同时只能有一个订单
   {
      Print("已有活跃订单，跳过信号");
      return;
   }
   
   ENUM_ORDER_TYPE order_type;
   if(signal.order_type == "BUY")
      order_type = ORDER_TYPE_BUY;
   else if(signal.order_type == "SELL")
      order_type = ORDER_TYPE_SELL;
   else
   {
      Print("未知的订单类型: ", signal.order_type);
      return;
   }
   
   // 获取当前价格
   double current_price;
   if(order_type == ORDER_TYPE_BUY)
      current_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   else
      current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   
   // 计算手数
   double lot_size = signal.lot_size;
   if(RiskPercent > 0 && !UseFixedLot)
   {
      lot_size = CalculateLotSize(signal.stop_loss, current_price);
   }
   
   // 标准化手数
   lot_size = NormalizeLot(lot_size);
   
   // 创建交易请求
   MqlTradeRequest request = {};
   MqlTradeResult result = {};
   
   request.action = TRADE_ACTION_DEAL;
   request.symbol = _Symbol;
   request.volume = lot_size;
   request.type = order_type;
   request.price = current_price;
   request.sl = signal.stop_loss;
   request.tp = signal.take_profit;
   request.magic = MagicNumber;
   request.comment = "CSV信号交易";
   
   // 发送交易请求
   bool success = OrderSend(request, result);
   
   if(success && result.retcode == TRADE_RETCODE_DONE)
   {
      Print("交易执行成功: ", 
            "类型=", EnumToString(order_type),
            ", 手数=", lot_size,
            ", 价格=", current_price,
            ", 止损=", signal.stop_loss,
            ", 止盈=", signal.take_profit);
   }
   else
   {
      Print("交易执行失败: 错误代码=", result.retcode, ", 描述=", result.comment);
   }
}

//+------------------------------------------------------------------+
//| 计算基于风险的手数                                               |
//+------------------------------------------------------------------+
double CalculateLotSize(double stop_loss, double entry_price)
{
   double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double risk_amount = account_balance * RiskPercent / 100.0;
   
   double stop_distance = MathAbs(entry_price - stop_loss);
   double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
   double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
   
   if(tick_size == 0 || tick_value == 0)
      return FixedLotSize;
   
   double money_per_lot = stop_distance / tick_size * tick_value;
   double lot_size = risk_amount / money_per_lot;
   
   return lot_size;
}

//+------------------------------------------------------------------+
//| 标准化手数                                                       |
//+------------------------------------------------------------------+
double NormalizeLot(double lot_size)
{
   double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
   
   if(lot_size < min_lot)
      lot_size = min_lot;
   if(lot_size > max_lot)
      lot_size = max_lot;
   
   // 调整到最近的步长
   lot_size = MathRound(lot_size / lot_step) * lot_step;
   
   return lot_size;
}

//+------------------------------------------------------------------+
//| 统计指定魔术数字的订单数量                                       |
//+------------------------------------------------------------------+
int CountOrdersByMagic()
{
   int count = 0;
   
   // 统计持仓
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionSelectByTicket(PositionGetTicket(i)))
      {
         if(PositionGetInteger(POSITION_MAGIC) == MagicNumber &&
            PositionGetString(POSITION_SYMBOL) == _Symbol)
         {
            count++;
         }
      }
   }
   
   // 统计挂单
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(OrderGetTicket(i)))
      {
         if(OrderGetInteger(ORDER_MAGIC) == MagicNumber &&
            OrderGetString(ORDER_SYMBOL) == _Symbol)
         {
            count++;
         }
      }
   }
   
   return count;
}

//+------------------------------------------------------------------+



