# MT5交易策略系统

这是一个完整的MT5交易策略系统，包含Python策略生成器和MQL5 EA，可以实现从策略开发到回测的完整流程。

## 系统组成

### 1. Python文件
- `strategy_generator.py` - 主要的策略生成器
- `config.py` - 配置文件，包含所有策略参数
- `requirements.txt` - Python依赖包列表

### 2. MQL5文件
- `CSVSignalEA.mq5` - MT5 EA文件，读取CSV信号并执行交易

### 3. 输出文件
- `trading_signals.csv` - 生成的交易信号文件
- `strategy_log.txt` - 策略运行日志

## 安装和设置

### 1. Python环境设置

```bash
# 安装必要的Python包
pip install MetaTrader5 pandas numpy
```

### 2. MT5设置

1. 确保MT5已安装并可以正常运行
2. 在MT5中启用算法交易
3. 将`CSVSignalEA.mq5`文件复制到MT5的`Experts`文件夹中
4. 编译EA文件

### 3. 配置参数

编辑`config.py`文件，设置您的交易参数：

```python
STRATEGY_CONFIG = {
    'symbol': 'EURUSD',              # 交易品种
    'timeframe': 'H1',               # 时间周期
    'lot_size': 0.01,                # 交易手数
    'stop_loss_points': 100,         # 止损点数
    'take_profit_points': 200,       # 止盈点数
    'history_days': 30,              # 历史数据天数
}
```

## 使用方法

### 第一步：生成交易信号

```bash
python strategy_generator.py
```

这将：
1. 连接到MT5
2. 获取历史数据
3. 计算技术指标
4. 生成交易信号
5. 导出到`trading_signals.csv`文件

### 第二步：在MT5中回测

1. 打开MT5策略测试器
2. 选择`CSVSignalEA`
3. 设置测试参数：
   - 品种：与Python策略中设置的相同
   - 时间范围：包含CSV文件中的信号时间
   - 模型：选择"每个价格变动"获得最高精度
4. 在EA参数中设置：
   - `CSVFileName`: "trading_signals.csv"
   - `EnableTrading`: true
   - 其他参数根据需要调整
5. 开始回测

## 策略逻辑

当前实现的是一个简单的移动平均线交叉策略：

### 买入信号
- 快速移动平均线(10周期)上穿慢速移动平均线(20周期)
- 且RSI < 70（避免超买区域买入）

### 卖出信号
- 快速移动平均线下穿慢速移动平均线
- 且RSI > 30（避免超卖区域卖出）

### 风险管理
- 每次交易固定手数：0.01手
- 固定止损：100点
- 固定止盈：200点（风险回报比1:2）

## 文件说明

### trading_signals.csv格式

```csv
DateTime,OrderType,Price,LotSize,StopLoss,TakeProfit
2024.01.15 10:00:00,BUY,1.08500,0.01,1.08400,1.08700
2024.01.16 14:00:00,SELL,1.08800,0.01,1.08900,1.08600
```

### EA参数说明

- `CSVFileName`: CSV文件名（相对于MT5数据文件夹）
- `EnableTrading`: 是否启用实际交易
- `MagicNumber`: 魔术数字，用于识别EA的订单
- `RiskPercent`: 风险百分比（如果不使用固定手数）
- `UseFixedLot`: 是否使用固定手数
- `FixedLotSize`: 固定手数大小

## 自定义策略

要实现自己的策略，可以修改`strategy_generator.py`中的以下方法：

1. `calculate_indicators()` - 添加新的技术指标
2. `generate_signals()` - 修改信号生成逻辑

例如，添加MACD指标：

```python
def calculate_indicators(self, df):
    # 现有指标...
    
    # 添加MACD
    exp1 = df['close'].ewm(span=12).mean()
    exp2 = df['close'].ewm(span=26).mean()
    df['macd'] = exp1 - exp2
    df['macd_signal'] = df['macd'].ewm(span=9).mean()
    df['macd_histogram'] = df['macd'] - df['macd_signal']
    
    return df
```

## 注意事项

1. **回测数据质量**：确保MT5有足够的历史数据
2. **时间同步**：Python和MT5的时间设置应该一致
3. **文件路径**：CSV文件应放在MT5能访问的位置
4. **风险管理**：这只是示例策略，实际交易前请充分测试
5. **滑点和点差**：回测结果可能与实际交易有差异

## 扩展功能

可以考虑添加的功能：
- 多品种交易支持
- 动态止损止盈
- 资金管理优化
- 更复杂的技术指标组合
- 实时信号生成
- 交易结果分析

## 故障排除

### 常见问题

1. **MT5连接失败**
   - 检查MT5是否正在运行
   - 确认账户信息正确
   - 检查防火墙设置

2. **CSV文件读取失败**
   - 确认文件路径正确
   - 检查文件格式是否正确
   - 确认MT5有文件读取权限

3. **交易执行失败**
   - 检查账户余额
   - 确认交易品种可用
   - 检查交易时间（市场是否开放）

## 联系和支持

如有问题或建议，请查看代码注释或修改配置参数进行调试。
