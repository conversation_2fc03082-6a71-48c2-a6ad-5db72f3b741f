"""
配置文件 - MT5交易策略参数设置
"""

# MT5连接配置
MT5_CONFIG = {
    'login': 313835981,        # 您的MT5账户号码
    'password': '@caoSHUO1990825',    # 您的MT5密码
    'server': 'XMGlobal-MT5 7',      # 您的MT5服务器
}

# 策略参数配置
STRATEGY_CONFIG = {
    # 基本参数
    'symbol': 'EURUSD',              # 交易品种
    'timeframe': 'H1',               # 时间周期 (M1, M5, M15, M30, H1, H4, D1)
    'lot_size': 0.01,                # 交易手数
    'stop_loss_points': 100,         # 止损点数
    'take_profit_points': 200,       # 止盈点数
    
    # 数据获取参数
    'start_date': '2024-01-01',      # 开始日期 (YYYY-MM-DD)
    'end_date': '2025-06-23',        # 结束日期 (YYYY-MM-DD)
    'use_date_range': True,          # 是否使用日期范围而不是相对天数
    'history_days': 30,              # 历史数据天数（当use_date_range为False时使用）
    
    # 技术指标参数
    'ma_fast_period': 10,            # 快速移动平均线周期
    'ma_slow_period': 20,            # 慢速移动平均线周期
    'rsi_period': 14,                # RSI周期
    'rsi_overbought': 70,            # RSI超买阈值
    'rsi_oversold': 30,              # RSI超卖阈值
}

# 输出文件配置
OUTPUT_CONFIG = {
    'csv_filename': 'trading_signals.csv',
    'log_filename': 'strategy_log.txt',
}

# MQL5 EA配置
EA_CONFIG = {
    'magic_number': 123456,          # 魔术数字
    'enable_trading': True,          # 是否启用交易
    'risk_percent': 1.0,             # 风险百分比
    'use_fixed_lot': True,           # 使用固定手数
    'max_positions': 1,              # 最大持仓数量
}

# 时间周期映射（Python到MQL5）
TIMEFRAME_MAPPING = {
    'M1': 1,      # 1分钟
    'M5': 5,      # 5分钟
    'M15': 15,    # 15分钟
    'M30': 30,    # 30分钟
    'H1': 60,     # 1小时
    'H4': 240,    # 4小时
    'D1': 1440,   # 1天
}

def get_mt5_timeframe(timeframe_str):
    """
    将字符串时间周期转换为MT5时间周期常量
    
    Args:
        timeframe_str: 时间周期字符串
        
    Returns:
        MT5时间周期常量
    """
    import MetaTrader5 as mt5
    
    mapping = {
        'M1': mt5.TIMEFRAME_M1,
        'M5': mt5.TIMEFRAME_M5,
        'M15': mt5.TIMEFRAME_M15,
        'M30': mt5.TIMEFRAME_M30,
        'H1': mt5.TIMEFRAME_H1,
        'H4': mt5.TIMEFRAME_H4,
        'D1': mt5.TIMEFRAME_D1,
    }
    
    return mapping.get(timeframe_str, mt5.TIMEFRAME_H1)

def validate_config():
    """验证配置参数的有效性"""
    errors = []
    
    # 验证策略参数
    if STRATEGY_CONFIG['lot_size'] <= 0:
        errors.append("交易手数必须大于0")
    
    if STRATEGY_CONFIG['stop_loss_points'] <= 0:
        errors.append("止损点数必须大于0")
    
    if STRATEGY_CONFIG['take_profit_points'] <= 0:
        errors.append("止盈点数必须大于0")
    
    if STRATEGY_CONFIG['history_days'] <= 0:
        errors.append("历史数据天数必须大于0")
    
    # 验证技术指标参数
    if STRATEGY_CONFIG['ma_fast_period'] >= STRATEGY_CONFIG['ma_slow_period']:
        errors.append("快速移动平均线周期必须小于慢速移动平均线周期")
    
    if not (0 < STRATEGY_CONFIG['rsi_oversold'] < STRATEGY_CONFIG['rsi_overbought'] < 100):
        errors.append("RSI阈值设置不正确")
    
    # 验证EA参数
    if not (0 < EA_CONFIG['risk_percent'] <= 100):
        errors.append("风险百分比必须在0-100之间")
    
    if errors:
        print("配置验证失败：")
        for error in errors:
            print(f"- {error}")
        return False
    
    print("配置验证通过")
    return True

if __name__ == "__main__":
    # 验证配置
    validate_config()
    
    # 打印当前配置
    print("\n当前策略配置：")
    for key, value in STRATEGY_CONFIG.items():
        print(f"{key}: {value}")
    
    print("\n当前EA配置：")
    for key, value in EA_CONFIG.items():
        print(f"{key}: {value}")



